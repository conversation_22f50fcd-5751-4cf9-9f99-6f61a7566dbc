# AnyIG Documentation

Welcome to AnyIG documentation! Choose your preferred language:

## 📚 Available Languages

### [🇺🇸 English](../README.md)
Complete documentation in English with all features, installation guides, and examples.

### [🇨🇳 简体中文](../README.zh-CN.md)
完整的中文文档，包含所有功能、安装指南和示例。

---

## 🚀 Quick Links

### Getting Started
- [Installation Guide](../README.md#installation) | [安装指南](../README.zh-CN.md#安装)
- [Quick Start](../README.md#quick-start) | [快速开始](../README.zh-CN.md#快速开始)

### Features
- [Available Templates](../README.md#available-templates) | [可用模板](../README.zh-CN.md#可用模板)
- [Configuration](../README.md#configuration) | [配置](../README.zh-CN.md#配置)
- [CLI Options](../README.md#cli-options) | [命令行选项](../README.zh-CN.md#命令行选项)

### Development
- [Development Guide](../README.md#development) | [开发指南](../README.zh-CN.md#开发)
- [Contributing](../README.md#contributing) | [贡献](../README.zh-CN.md#贡献)

---

## 🔧 Template Categories

| Category | English | 中文 |
|----------|---------|------|
| Version Control | Git ignore files | Git 版本控制忽略文件 |
| Package Managers | NPM ignore files | NPM 包管理器忽略文件 |
| Code Quality | ESLint, Prettier | 代码质量工具 |
| Build Tools | Docker, Babel | 构建工具 |
| Frameworks | React, Vue, Next.js | 前端框架 |
| Languages | Python, Java | 编程语言 |
| Editors | VS Code, EditorConfig | 编辑器配置 |

## 📖 Examples

### Basic Usage
```bash
# Interactive mode / 交互模式
anyig

# Generate specific template / 生成特定模板
anyig -t gitignore

# Multiple selection / 多选模式
anyig -m
```

### Configuration Example
```json
{
  "defaultTemplates": ["gitignore", "npmignore"],
  "outputDir": "./generated",
  "autoBackup": true,
  "confirmOverwrite": false
}
```

---

## 🌐 Language Switching

You can switch between languages at any time by clicking the language links at the top of each README file:

- **English**: [README.md](../README.md)
- **简体中文**: [README.zh-CN.md](../README.zh-CN.md)

Both versions contain the same comprehensive information, just in different languages to serve our global community better.

---

## 📞 Support

Need help? Choose your preferred language for support:

### English Support
- 🐛 [Report Issues](https://github.com/zhangyu-521/ig/issues)
- 💬 [Discussions](https://github.com/zhangyu-521/ig/discussions)

### 中文支持
- 🐛 [问题反馈](https://github.com/zhangyu-521/ig/issues)
- 💬 [讨论区](https://github.com/zhangyu-521/ig/discussions)
- 📧 邮箱: <EMAIL>

---

Made with ❤️ by [zhangyu620](https://github.com/zhangyu-521)
