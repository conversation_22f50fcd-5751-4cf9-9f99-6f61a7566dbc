{"name": "anyig", "version": "2.0.2", "description": "A powerful and extensible tool for generating ignore files for various development tools and environments", "repository": {"type": "git", "url": "https://github.com/zhangyu-521/ig"}, "bugs": {"url": "https://github.com/zhangyu-521/ig/issues"}, "homepage": "https://github.com/zhangyu-521/ig#readme", "engines": {"node": ">=16.0.0"}, "bin": "./dist/bin/bin.js", "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "files": ["dist/**/*", "README.md", "LICENSE"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts bin/**/*.ts", "lint:fix": "eslint src/**/*.ts bin/**/*.ts --fix", "format": "prettier --write \"src/**/*.ts\" \"bin/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"bin/**/*.ts\"", "typecheck": "tsc --noEmit", "docs": "node scripts/docs-switch.js", "docs:en": "node scripts/docs-switch.js en", "docs:zh": "node scripts/docs-switch.js zh-CN", "docs:serve": "npx http-server docs -p 8080 -o"}, "keywords": ["ignore", "gitignore", "npmignore", "<PERSON><PERSON><PERSON><PERSON>", "prettierignore", "dockerignore", "cli", "template", "generator", "development-tools"], "author": "zhangyu620", "license": "MIT", "dependencies": {"commander": "^11.1.0", "inquirer": "^9.2.12"}, "devDependencies": {"@types/inquirer": "^9.0.8", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "^5.3.2"}}